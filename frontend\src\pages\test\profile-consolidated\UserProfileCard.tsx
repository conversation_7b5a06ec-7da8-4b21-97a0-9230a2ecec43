import {
  CalendarOutlined,
  CarOutlined,
  CheckOutlined,
  EditOutlined,
  LogoutOutlined,
  MailOutlined,
  PhoneOutlined,
  SettingOutlined,
  TagOutlined,
  TeamOutlined,
  UserOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import {
  Avatar,
  Button,
  Card,
  Col,
  Divider,
  Dropdown,
  Flex,
  Form,
  Input,
  Menu,
  Modal,
  Row,
  Space,
  Steps,
  Tag,
  Tooltip,
  Typography,
  Spin,
  Alert,
} from "antd";
import React, { useState, useEffect } from "react";
import { UserService } from "@/services/user";
import { AuthService } from "@/services";
import { useModel, history } from '@umijs/max';
import type { UserPersonalStatsResponse, UserProfileDetailResponse } from "@/types/api";

const { Title, Text } = Typography;
const { Step } = Steps;

const UserProfileCard: React.FC = () => {
  // 用户详细信息状态
  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({
    name: "",
    position: "",
    email: "",
    telephone: "",
    registerDate: "",
    lastLoginTime: "",
    lastLoginTeam: "",
    teamCount: 0,
    avatar: "",
  });
  const [userInfoLoading, setUserInfoLoading] = useState(true);
  const [userInfoError, setUserInfoError] = useState<string | null>(null);

  // 个人统计数据状态
  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({
    vehicles: 0,
    personnel: 0,
    warnings: 0,
    alerts: 0,
  });
  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);

  // 订阅计划数据
  const subscriptionPlans = [
    {
      id: "basic",
      name: "基础版",
      price: 0,
      description: "适合小团队使用",
      features: ["最多5个团队", "最多20辆车辆", "基础安全监控", "基本报告功能"],
    },
    {
      id: "professional",
      name: "专业版",
      price: 199,
      description: "适合中小型企业",
      features: [
        "最多20个团队",
        "最多100辆车辆",
        "高级安全监控",
        "详细分析报告",
        "设备状态预警",
        "优先技术支持",
      ],
    },
    {
      id: "enterprise",
      name: "企业版",
      price: 499,
      description: "适合大型企业",
      features: [
        "不限团队数量",
        "不限车辆数量",
        "AI安全分析",
        "实时监控告警",
        "定制化报告",
        "专属客户经理",
        "24/7技术支持",
      ],
    },
  ];

  // 当前订阅信息
  const currentSubscription = {
    planId: "basic",
    expires: "2025-12-31",
  };

  // 状态管理
  const [editProfileModalVisible, setEditProfileModalVisible] = useState(false);
  const [subscriptionModalVisible, setSubscriptionModalVisible] =
    useState(false);
  const [logoutModalVisible, setLogoutModalVisible] = useState(false);
  const [logoutLoading, setLogoutLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [editProfileForm] = Form.useForm();

  const { setInitialState } = useModel('@@initialState');

  // 获取用户数据
  useEffect(() => {
    console.log('UserProfileCard: useEffect 开始执行');

    const fetchUserData = async () => {
      try {
        console.log('UserProfileCard: 开始获取用户数据');

        // 分别获取用户详细信息和统计数据，避免一个失败影响另一个
        const userDetailPromise = UserService.getUserProfileDetail().catch(error => {
          console.error('获取用户详细信息失败:', error);
          setUserInfoError('获取用户详细信息失败，请稍后重试');
          return null;
        });

        const statsPromise = UserService.getUserPersonalStats().catch(error => {
          console.error('获取统计数据失败:', error);
          setStatsError('获取统计数据失败，请稍后重试');
          return null;
        });

        const [userDetail, stats] = await Promise.all([userDetailPromise, statsPromise]);

        if (userDetail) {
          console.log('UserProfileCard: 获取到用户详细信息:', userDetail);
          setUserInfo(userDetail);
          setUserInfoError(null);
        }

        if (stats) {
          console.log('UserProfileCard: 获取到统计数据:', stats);
          setPersonalStats(stats);
          setStatsError(null);
        }

      } catch (error) {
        console.error('获取用户数据时发生未知错误:', error);
        setUserInfoError('获取用户数据失败，请刷新页面重试');
        setStatsError('获取统计数据失败，请刷新页面重试');
      } finally {
        setUserInfoLoading(false);
        setStatsLoading(false);
      }
    };

    fetchUserData();
  }, []);

  // 退出登录处理函数
  const handleLogout = async () => {
    try {
      setLogoutLoading(true);

      // 调用退出登录API
      await AuthService.logout();

      // 清除 initialState
      if (setInitialState) {
        await setInitialState({
          currentUser: undefined,
          currentTeam: undefined,
        });
      }

      // 跳转到登录页面
      history.push('/user/login');

    } catch (error) {
      console.error('退出登录失败:', error);
      // 即使API调用失败，也要清除本地状态并跳转
      if (setInitialState) {
        await setInitialState({
          currentUser: undefined,
          currentTeam: undefined,
        });
      }
      history.push('/user/login');
    } finally {
      setLogoutLoading(false);
      setLogoutModalVisible(false);
    }
  };

  return (
    <>
      <Card
        className="dashboard-card"
        style={{
          borderRadius: 12,
          boxShadow: "0 2px 8px rgba(0,0,0,0.05)",
          border: "none",
          background: "linear-gradient(145deg, #ffffff, #f0f7ff)",
          position: "relative",
        }}
      >
        {/* 设置按钮和退出登录按钮容器 */}
        <Flex
          gap={2}
          align="center"
          style={{
            position: "absolute",
            top: 12,
            right: 12,
            zIndex: 1,
          }}
        >
          <Dropdown
            overlay={
              <Menu>
                <Menu.Item
                  key="editProfile"
                  icon={<EditOutlined />}
                  onClick={() => {
                    setEditProfileModalVisible(true);
                    setCurrentStep(0);
                    editProfileForm.setFieldsValue({
                      name: userInfo.name,
                      email: userInfo.email,
                      telephone: userInfo.telephone,
                    });
                  }}
                >
                  修改资料
                </Menu.Item>
                <Menu.Item
                  key="subscription"
                  icon={<TagOutlined />}
                  onClick={() => setSubscriptionModalVisible(true)}
                >
                  订阅套餐
                </Menu.Item>
              </Menu>
            }
            trigger={["click"]}
          >
            <Button
              type="text"
              icon={
                <SettingOutlined style={{ fontSize: 16, color: "#8c8c8c" }} />
              }
              style={{ padding: "4px 6px" }}
            />
          </Dropdown>

          {/* 退出登录按钮 */}
          <Button
            type="text"
            icon={<LogoutOutlined style={{ fontSize: 16, color: "#8c8c8c" }} />}
            onClick={() => setLogoutModalVisible(true)}
            style={{ padding: "4px 6px" }}
          />
        </Flex>

        {/* 两列布局 */}
        {userInfoError ? (
          <Alert
            message="用户信息加载失败"
            description={userInfoError}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
        ) : (
          <Spin spinning={userInfoLoading}>
            <Row gutter={0} style={{ margin: 0 }}>
              {/* 第一列：姓名、头像、基础信息和最后登录信息 */}
              <Col xs={24} sm={10}>
                <Flex vertical>
                  {/* 用户基本信息区块 */}
                  <Flex align="center">
                    <Space direction="vertical" size={0}>
                      <Flex align="center" style={{ marginBottom: 4 }}>
                        <Title level={4} style={{ margin: 0, fontSize: 25 }}>
                          {userInfo.name || "加载中..."}
                        </Title>
                      </Flex>
                      {/* 邮箱、电话和注册日期信息 */}
                      <Flex wrap="wrap" gap={6}>
                        {userInfo.email && (
                          <Tag
                            icon={<MailOutlined style={{ fontSize: 12 }} />}
                            style={{ fontSize: 13 }}
                          >
                            {userInfo.email}
                          </Tag>
                        )}
                        {userInfo.telephone && (
                          <Tag
                            icon={<PhoneOutlined style={{ fontSize: 12 }} />}
                            style={{ fontSize: 13 }}
                          >
                            {userInfo.telephone}
                          </Tag>
                        )}
                        {userInfo.registerDate && (
                          <Tag
                            icon={<CalendarOutlined style={{ fontSize: 12 }} />}
                            style={{ fontSize: 13 }}
                          >
                            {userInfo.registerDate}
                          </Tag>
                        )}
                      </Flex>
                    </Space>
                  </Flex>
                  
                  {/* 最后登录信息放在一行（位于手机号下方） */}
                  <Flex wrap="wrap" gap={12} style={{ marginTop: 8 }}>
                    <Flex align="center">
                      <CalendarOutlined style={{ color: "#8c8c8c", marginRight: 6, fontSize: 12 }} />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        最后登录时间: <Text strong>{userInfo.lastLoginTime || ""}</Text>
                      </Text>
                    </Flex>
                    <Flex align="center">
                      <TeamOutlined style={{ color: "#8c8c8c", marginRight: 6, fontSize: 12 }} />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        最后登录团队: <Text strong>{userInfo.lastLoginTeam}</Text>
                      </Text>
                    </Flex>
                    <Flex align="center">
                      <TeamOutlined style={{ color: "#8c8c8c", marginRight: 6, fontSize: 12 }} />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        团队总数: <Text strong>{userInfo.teamCount}</Text>
                      </Text>
                    </Flex>
                  </Flex>
                </Flex>
              </Col>

              {/* 第二列：车辆人员预警告警统计 */}
              <Col xs={24} sm={14}>
                {statsError ? (
                  <Alert
                    message="统计数据加载失败"
                    description={statsError}
                    type="error"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />
                ) : (
                  <Spin spinning={statsLoading}>
                    <Row gutter={8} justify="space-around">
                      {/* 车辆统计 */}
                      <Col>
                        <Space direction="vertical">
                          <Flex
                            align="center"
                            style={{ color: "#595959", fontSize: 12 }}
                          >
                            <CarOutlined />
                            <Text style={{ marginLeft: 4 }}>车辆</Text>
                          </Flex>
                          <Text
                            strong
                            style={{
                              fontSize: 24,
                              color: "#1890ff",
                              fontWeight: 600,
                              lineHeight: "28px",
                            }}
                          >
                            {personalStats.vehicles}
                          </Text>
                        </Space>
                      </Col>

                      {/* 人员统计 */}
                      <Col>
                        <Space direction="vertical">
                          <Flex
                            align="center"
                            style={{ color: "#595959", fontSize: 12 }}
                          >
                            <UserOutlined />
                            <Text style={{ marginLeft: 4 }}>人员</Text>
                          </Flex>
                          <Text
                            strong
                            style={{
                              fontSize: 24,
                              color: "#52c41a",
                              fontWeight: 600,
                              lineHeight: "28px",
                            }}
                          >
                            {personalStats.personnel}
                          </Text>
                        </Space>
                      </Col>

                      {/* 预警统计 */}
                      <Col>
                        <Space direction="vertical">
                          <Flex
                            align="center"
                            style={{ color: "#595959", fontSize: 12 }}
                          >
                            <WarningOutlined />
                            <Text style={{ marginLeft: 4 }}>预警</Text>
                          </Flex>
                          <Text
                            strong
                            style={{
                              fontSize: 24,
                              color: "#faad14",
                              fontWeight: 600,
                              lineHeight: "28px",
                            }}
                          >
                            {personalStats.warnings}
                          </Text>
                        </Space>
                      </Col>

                      {/* 告警统计 */}
                      <Col>
                        <Space direction="vertical">
                          <Flex
                            align="center"
                            style={{ color: "#595959", fontSize: 12 }}
                          >
                            <WarningOutlined style={{ color: "#ff4d4f" }} />
                            <Text style={{ marginLeft: 4 }}>告警</Text>
                          </Flex>
                          <Text
                            strong
                            style={{
                              fontSize: 24,
                              color: "#ff4d4f",
                              fontWeight: 600,
                              lineHeight: "28px",
                            }}
                          >
                            {personalStats.alerts}
                          </Text>
                        </Space>
                      </Col>
                    </Row>
                  </Spin>
                )}
              </Col>
            </Row>
          </Spin>
        )}
      </Card>

      {/* 修改资料模态框 */}
      <Modal
        title="修改个人资料"
        open={editProfileModalVisible}
        onCancel={() => {
          setEditProfileModalVisible(false);
          setCurrentStep(0);
        }}
        footer={[
          currentStep === 1 && (
            <Button key="back" onClick={() => setCurrentStep(0)}>
              上一步
            </Button>
          ),
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              if (currentStep === 0) {
                editProfileForm.validateFields().then(() => {
                  setCurrentStep(1);
                });
              } else {
                editProfileForm.validateFields().then((values) => {
                  console.log("个人资料表单值:", values);
                  // 提交表单，这里简化处理，只输出到控制台
                  setEditProfileModalVisible(false);
                  setCurrentStep(0);
                });
              }
            }}
          >
            {currentStep === 0 ? "下一步" : "确定"}
          </Button>,
        ]}
      >
        <Steps current={currentStep} style={{ marginBottom: 16 }}>
          <Step title="填写信息" />
          <Step title="安全验证" />
        </Steps>

        <Form form={editProfileForm} layout="vertical" hideRequiredMark>
          {currentStep === 0 ? (
            <>
              <Form.Item
                name="name"
                label="用户名"
                rules={[{ required: true, message: "请输入用户名" }]}
              >
                <Input placeholder="请输入用户名" />
              </Form.Item>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: "请输入邮箱地址" },
                  { type: "email", message: "请输入有效的邮箱地址" },
                ]}
              >
                <Input placeholder="请输入邮箱地址" />
              </Form.Item>
              <Form.Item
                name="telephone"
                label="手机号"
                rules={[
                  { required: true, message: "请输入手机号" },
                  { pattern: /^1\d{10}$/, message: "请输入有效的手机号" },
                ]}
              >
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </>
          ) : (
            <div style={{ textAlign: "center" }}>
              <div style={{ margin: "12px 0", textAlign: "center" }}>
                <Text>
                  验证码已发送至您的手机号{" "}
                  <Text strong>{editProfileForm.getFieldValue("telephone")}</Text>
                </Text>
              </div>
              <Form.Item
                name="verificationCode"
                label="验证码"
                rules={[{ required: true, message: "请输入验证码" }]}
              >
                <Input
                  placeholder="请输入6位验证码"
                  maxLength={6}
                  style={{ width: "50%", textAlign: "center" }}
                />
              </Form.Item>
              <Button type="link" style={{ padding: 0 }}>
                重新发送验证码
              </Button>
            </div>
          )}
        </Form>
      </Modal>

      {/* 订阅套餐模态框 */}
      <Modal
        title="订阅套餐"
        open={subscriptionModalVisible}
        onCancel={() => setSubscriptionModalVisible(false)}
        footer={null}
        width={800}
      >
        <div
          style={{
            background: "#f9f9f9",
            padding: 12,
            borderRadius: 8,
            marginBottom: 16,
          }}
        >
          <Flex justify="space-between" align="center">
            <Text strong>当前套餐: </Text>
            <Tag color="green" style={{ marginLeft: 8, fontSize: 13 }}>
              {
                subscriptionPlans.find(
                  (p) => p.id === currentSubscription.planId
                )?.name
              }
            </Tag>
            <Text type="secondary">
              到期时间: {currentSubscription.expires}
            </Text>
          </Flex>
        </div>

        <Row gutter={24}>
          {subscriptionPlans.map((plan) => (
            <Col span={8} key={plan.id}>
              <div
                style={{
                  height: "100%",
                  borderRadius: 8,
                  border: `1px solid ${
                    plan.id === currentSubscription.planId
                      ? "#52c41a"
                      : "#d9d9d9"
                  }`,
                  background: "#fff",
                  position: "relative",
                  overflow: "hidden",
                }}
              >
                {plan.id === currentSubscription.planId && (
                  <Tag
                    color="green"
                    style={{
                      position: "absolute",
                      top: -10,
                      right: -10,
                      borderRadius: 2,
                      boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                    }}
                  >
                    当前套餐
                  </Tag>
                )}
                <div style={{ padding: 16 }}>
                  <Title
                    level={4}
                    style={{ textAlign: "center", margin: "12px 0 8px" }}
                  >
                    {plan.name}
                  </Title>
                  <Flex vertical align="center" style={{ marginBottom: 12 }}>
                    {plan.price > 0 ? (
                      <>
                        <Title level={2} style={{ marginBottom: 0 }}>
                          ¥{plan.price}
                        </Title>
                        <Text type="secondary">/月</Text>
                      </>
                    ) : (
                      <Title
                        level={2}
                        style={{ color: "#52c41a", marginBottom: 0 }}
                      >
                        免费
                      </Title>
                    )}
                    <Text type="secondary" style={{ marginTop: 4 }}>
                      {plan.description}
                    </Text>
                  </Flex>

                  <Divider style={{ margin: "8px 0" }} />

                  <div style={{ minHeight: 170 }}>
                    {plan.features.map((feature, index) => (
                      <Space
                        key={index}
                        align="start"
                        style={{ marginBottom: 6 }}
                      >
                        <CheckOutlined
                          style={{
                            color: "#52c41a",
                            marginRight: 8,
                            marginTop: 4,
                          }}
                        />
                        <Text>{feature}</Text>
                      </Space>
                    ))}
                  </div>

                  {plan.id !== currentSubscription.planId ? (
                    <Button
                      type="primary"
                      block
                      style={{
                        marginTop: 12,
                        boxShadow: "0 2px 8px rgba(24, 144, 255, 0.3)",
                      }}
                      onClick={() => {
                        console.log("选择套餐:", plan);
                        setSubscriptionModalVisible(false);
                      }}
                    >
                      立即订阅
                    </Button>
                  ) : (
                    <Button
                      block
                      style={{
                        marginTop: 12,
                        background: "#f6ffed",
                        borderColor: "#b7eb8f",
                        color: "#389e0d",
                      }}
                      disabled
                    >
                      当前套餐
                    </Button>
                  )}
                </div>
              </div>
            </Col>
          ))}
        </Row>

        <Flex justify="center" style={{ marginTop: 20 }}>
          <Text type="secondary">订阅服务自动续费，可随时取消</Text>
        </Flex>
      </Modal>

      {/* 退出登录确认模态框 */}
      <Modal
        title="确认退出登录"
        open={logoutModalVisible}
        onCancel={() => setLogoutModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setLogoutModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="confirm"
            type="primary"
            danger
            loading={logoutLoading}
            onClick={handleLogout}
          >
            确认退出
          </Button>,
        ]}
        width={400}
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <LogoutOutlined style={{ fontSize: 48, color: '#ff4d4f', marginBottom: 16 }} />
          <div style={{ marginBottom: 8 }}>
            <Text strong style={{ fontSize: 16 }}>您确定要退出登录吗？</Text>
          </div>
          <Text type="secondary">
            退出后您需要重新登录才能继续使用系统
          </Text>
        </div>
      </Modal>
    </>
  );
};

export default UserProfileCard;