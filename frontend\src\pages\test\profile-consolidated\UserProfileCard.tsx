import {
  CalendarOutlined,
  CarOutlined,
  CheckOutlined,
  EditOutlined,
  LogoutOutlined,
  MailOutlined,
  PhoneOutlined,
  SettingOutlined,
  TagOutlined,
  UserOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Col,
  Divider,
  Dropdown,
  Flex,
  Form,
  Input,
  Modal,
  Row,
  Space,
  Steps,
  Tag,
  Tooltip,
  Typography,
  Spin,
  Alert,
} from "antd";
import React, { useState, useEffect } from "react";
import { UserService } from "@/services/user";
import { AuthService } from "@/services";
import { useModel, history } from '@umijs/max';
import type { UserPersonalStatsResponse, UserProfileDetailResponse } from "@/types/api";

const { Title, Text } = Typography;
const { Step } = Steps;

const UserProfileCard: React.FC = () => {
  // 用户详细信息状态
  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({
    name: "",
    position: "",
    email: "",
    phone: "",
    telephone: "",
    registerDate: "",
    lastLoginTime: "",
    lastLoginTeam: "",
    teamCount: 0,
    avatar: "",
  });
  const [userInfoLoading, setUserInfoLoading] = useState(true);
  const [userInfoError, setUserInfoError] = useState<string | null>(null);

  // 个人统计数据状态
  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({
    vehicles: 0,
    personnel: 0,
    warnings: 0,
    alerts: 0,
  });
  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);

  // 订阅计划数据
  const subscriptionPlans = [
    {
      id: "basic",
      name: "基础版",
      price: 0,
      description: "适合小团队使用",
      features: ["最多5个团队", "最多20辆车辆", "基础安全监控", "基本报告功能"],
    },
    {
      id: "professional",
      name: "专业版",
      price: 199,
      description: "适合中小型企业",
      features: [
        "最多20个团队",
        "最多100辆车辆",
        "高级安全监控",
        "详细分析报告",
        "设备状态预警",
        "优先技术支持",
      ],
    },
    {
      id: "enterprise",
      name: "企业版",
      price: 499,
      description: "适合大型企业",
      features: [
        "不限团队数量",
        "不限车辆数量",
        "AI安全分析",
        "实时监控告警",
        "定制化报告",
        "专属客户经理",
        "24/7技术支持",
      ],
    },
  ];

  // 当前订阅信息
  const currentSubscription = {
    planId: "basic",
    expires: "2025-12-31",
  };

  // 状态管理
  const [editProfileModalVisible, setEditProfileModalVisible] = useState(false);
  const [subscriptionModalVisible, setSubscriptionModalVisible] =
    useState(false);
  const [logoutModalVisible, setLogoutModalVisible] = useState(false);
  const [logoutLoading, setLogoutLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [editProfileForm] = Form.useForm();

  const { setInitialState } = useModel('@@initialState');

  // 获取用户数据
  useEffect(() => {
    console.log('UserProfileCard: useEffect 开始执行');

    const fetchUserData = async () => {
      try {
        console.log('UserProfileCard: 开始获取用户数据');

        // 分别获取用户详细信息和统计数据，避免一个失败影响另一个
        const userDetailPromise = UserService.getUserProfileDetail().catch(error => {
          console.error('获取用户详细信息失败:', error);
          setUserInfoError('获取用户详细信息失败，请稍后重试');
          return null;
        });

        const statsPromise = UserService.getUserPersonalStats().catch(error => {
          console.error('获取统计数据失败:', error);
          setStatsError('获取统计数据失败，请稍后重试');
          return null;
        });

        const [userDetail, stats] = await Promise.all([userDetailPromise, statsPromise]);

        if (userDetail) {
          console.log('UserProfileCard: 获取到用户详细信息:', userDetail);
          setUserInfo(userDetail);
          setUserInfoError(null);
        }

        if (stats) {
          console.log('UserProfileCard: 获取到统计数据:', stats);
          setPersonalStats(stats);
          setStatsError(null);
        }

      } catch (error) {
        console.error('获取用户数据时发生未知错误:', error);
        setUserInfoError('获取用户数据失败，请刷新页面重试');
        setStatsError('获取统计数据失败，请刷新页面重试');
      } finally {
        setUserInfoLoading(false);
        setStatsLoading(false);
      }
    };

    fetchUserData();
  }, []);

  // 退出登录处理函数
  const handleLogout = async () => {
    try {
      setLogoutLoading(true);

      // 调用退出登录API
      await AuthService.logout();

      // 清除 initialState
      if (setInitialState) {
        await setInitialState({
          currentUser: undefined,
          currentTeam: undefined,
        });
      }

      // 跳转到登录页面
      history.push('/user/login');

    } catch (error) {
      console.error('退出登录失败:', error);
      // 即使API调用失败，也要清除本地状态并跳转
      if (setInitialState) {
        await setInitialState({
          currentUser: undefined,
          currentTeam: undefined,
        });
      }
      history.push('/user/login');
    } finally {
      setLogoutLoading(false);
      setLogoutModalVisible(false);
    }
  };

  return (
    <>
      <Card
        className="dashboard-card"
        style={{
          borderRadius: 16,
          boxShadow: "0 4px 20px rgba(0,0,0,0.08)",
          border: "1px solid rgba(0,0,0,0.06)",
          background: "#ffffff",
          position: "relative",
          overflow: "hidden",
        }}
        styles={{ body: { padding: 24 } }}
      >
        {/* 操作按钮区域 */}
        <div
          style={{
            position: "absolute",
            top: 20,
            right: 20,
            zIndex: 10,
            display: "flex",
            gap: 8,
          }}
        >
          <Tooltip title="设置">
            <Dropdown
              menu={{
                items: [
                  {
                    key: "editProfile",
                    icon: <EditOutlined />,
                    label: "修改资料",
                    onClick: () => {
                      setEditProfileModalVisible(true);
                      setCurrentStep(0);
                      editProfileForm.setFieldsValue({
                        name: userInfo.name,
                        email: userInfo.email,
                        telephone: userInfo.phone || userInfo.telephone,
                      });
                    },
                  },
                  {
                    key: "subscription",
                    icon: <TagOutlined />,
                    label: "订阅套餐",
                    onClick: () => setSubscriptionModalVisible(true),
                  },
                ],
              }}
              trigger={["click"]}
              placement="bottomRight"
            >
              <Button
                type="text"
                shape="circle"
                icon={<SettingOutlined />}
                style={{
                  color: "#8c8c8c",
                  backgroundColor: "rgba(0,0,0,0.04)",
                  border: "none",
                  transition: "all 0.2s",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "rgba(0,0,0,0.08)";
                  e.currentTarget.style.color = "#595959";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "rgba(0,0,0,0.04)";
                  e.currentTarget.style.color = "#8c8c8c";
                }}
              />
            </Dropdown>
          </Tooltip>

          <Tooltip title="退出登录">
            <Button
              type="text"
              shape="circle"
              icon={<LogoutOutlined />}
              onClick={() => setLogoutModalVisible(true)}
              style={{
                color: "#8c8c8c",
                backgroundColor: "rgba(0,0,0,0.04)",
                border: "none",
                transition: "all 0.2s",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = "rgba(255,77,79,0.1)";
                e.currentTarget.style.color = "#ff4d4f";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "rgba(0,0,0,0.04)";
                e.currentTarget.style.color = "#8c8c8c";
              }}
            />
          </Tooltip>
        </div>

        {/* 主要内容区域 */}
        <div style={{ paddingRight: 70 }}>
          {userInfoError ? (
            <Alert
              message="用户信息加载失败"
              description={userInfoError}
              type="error"
              showIcon
              style={{ marginBottom: 24 }}
            />
          ) : (
            <Spin spinning={userInfoLoading}>
              {/* 用户基本信息区域 */}
              <div style={{ marginBottom: 24 }}>
                <div style={{ marginBottom: 16, textAlign: "center" }}>
                  <Title
                    level={2}
                    style={{
                      margin: 0,
                      marginBottom: 8,
                      fontSize: 32,
                      fontWeight: 600,
                      color: "#262626",
                    }}
                  >
                    {userInfo.name || "加载中..."}
                  </Title>
                  {userInfo.position && (
                    <Text
                      style={{
                        fontSize: 18,
                        color: "#8c8c8c",
                        fontWeight: 400,
                      }}
                    >
                      {userInfo.position}
                    </Text>
                  )}
                </div>

                {/* 联系信息标签 */}
                <div style={{ marginBottom: 16, textAlign: "center" }}>
                  <Space wrap size={[8, 8]}>
                    {userInfo.email && (
                      <Tag
                        icon={<MailOutlined />}
                        style={{
                          fontSize: 14,
                          padding: "4px 12px",
                          borderRadius: 20,
                          border: "1px solid #e8f4ff",
                          backgroundColor: "#f0f9ff",
                          color: "#1890ff",
                        }}
                      >
                        {userInfo.email}
                      </Tag>
                    )}
                    {(userInfo.phone || userInfo.telephone) && (
                      <Tag
                        icon={<PhoneOutlined />}
                        style={{
                          fontSize: 14,
                          padding: "4px 12px",
                          borderRadius: 20,
                          border: "1px solid #e6f7ff",
                          backgroundColor: "#f0f9ff",
                          color: "#1890ff",
                        }}
                      >
                        {userInfo.phone || userInfo.telephone}
                      </Tag>
                    )}
                    {userInfo.registerDate && (
                      <Tag
                        icon={<CalendarOutlined />}
                        style={{
                          fontSize: 14,
                          padding: "4px 12px",
                          borderRadius: 20,
                          border: "1px solid #f0f0f0",
                          backgroundColor: "#fafafa",
                          color: "#8c8c8c",
                        }}
                      >
                        注册于 {userInfo.registerDate}
                      </Tag>
                    )}
                  </Space>
                </div>

                {/* 最后登录信息 */}
                <div
                  style={{
                    backgroundColor: "#fafafa",
                    borderRadius: 12,
                    padding: 16,
                    marginBottom: 20,
                  }}
                >
                  <div style={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
                    <CalendarOutlined style={{ color: "#8c8c8c", marginRight: 8, fontSize: 14 }} />
                    <div>
                      <Text type="secondary" style={{ fontSize: 12, display: "block" }}>
                        最后登录时间
                      </Text>
                      <Text strong style={{ fontSize: 14 }}>
                        {userInfo.lastLoginTime || "暂无记录"}
                      </Text>
                    </div>
                  </div>
                </div>
              </div>

              {/* 统计数据区域 */}
              <div>
                <Title level={4} style={{ marginBottom: 20, color: "#262626", textAlign: "center" }}>
                  数据统计
                </Title>
                {statsError ? (
                  <Alert
                    message="统计数据加载失败"
                    description={statsError}
                    type="error"
                    showIcon
                  />
                ) : (
                  <Spin spinning={statsLoading}>
                    <div
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(4, 1fr)",
                        gap: 16,
                        maxWidth: 600,
                        margin: "0 auto",
                      }}
                    >
                      {/* 车辆统计 */}
                      <div
                        style={{
                          backgroundColor: "#f0f9ff",
                          borderRadius: 12,
                          padding: 16,
                          textAlign: "center",
                          border: "1px solid #e6f7ff",
                        }}
                      >
                        <CarOutlined
                          style={{
                            fontSize: 20,
                            color: "#1890ff",
                            marginBottom: 6,
                          }}
                        />
                        <div>
                          <Text
                            style={{
                              fontSize: 28,
                              fontWeight: 700,
                              color: "#1890ff",
                              display: "block",
                              lineHeight: 1,
                            }}
                          >
                            {personalStats.vehicles}
                          </Text>
                          <Text
                            style={{
                              fontSize: 12,
                              color: "#595959",
                              marginTop: 4,
                            }}
                          >
                            车辆
                          </Text>
                        </div>
                      </div>

                      {/* 人员统计 */}
                      <div
                        style={{
                          backgroundColor: "#f6ffed",
                          borderRadius: 12,
                          padding: 16,
                          textAlign: "center",
                          border: "1px solid #d9f7be",
                        }}
                      >
                        <UserOutlined
                          style={{
                            fontSize: 20,
                            color: "#52c41a",
                            marginBottom: 6,
                          }}
                        />
                        <div>
                          <Text
                            style={{
                              fontSize: 28,
                              fontWeight: 700,
                              color: "#52c41a",
                              display: "block",
                              lineHeight: 1,
                            }}
                          >
                            {personalStats.personnel}
                          </Text>
                          <Text
                            style={{
                              fontSize: 12,
                              color: "#595959",
                              marginTop: 4,
                            }}
                          >
                            人员
                          </Text>
                        </div>
                      </div>

                      {/* 预警统计 */}
                      <div
                        style={{
                          backgroundColor: "#fffbe6",
                          borderRadius: 12,
                          padding: 16,
                          textAlign: "center",
                          border: "1px solid #ffe58f",
                        }}
                      >
                        <WarningOutlined
                          style={{
                            fontSize: 20,
                            color: "#faad14",
                            marginBottom: 6,
                          }}
                        />
                        <div>
                          <Text
                            style={{
                              fontSize: 28,
                              fontWeight: 700,
                              color: "#faad14",
                              display: "block",
                              lineHeight: 1,
                            }}
                          >
                            {personalStats.warnings}
                          </Text>
                          <Text
                            style={{
                              fontSize: 12,
                              color: "#595959",
                              marginTop: 4,
                            }}
                          >
                            预警
                          </Text>
                        </div>
                      </div>

                      {/* 告警统计 */}
                      <div
                        style={{
                          backgroundColor: "#fff2f0",
                          borderRadius: 12,
                          padding: 16,
                          textAlign: "center",
                          border: "1px solid #ffccc7",
                        }}
                      >
                        <WarningOutlined
                          style={{
                            fontSize: 20,
                            color: "#ff4d4f",
                            marginBottom: 6,
                          }}
                        />
                        <div>
                          <Text
                            style={{
                              fontSize: 28,
                              fontWeight: 700,
                              color: "#ff4d4f",
                              display: "block",
                              lineHeight: 1,
                            }}
                          >
                            {personalStats.alerts}
                          </Text>
                          <Text
                            style={{
                              fontSize: 12,
                              color: "#595959",
                              marginTop: 4,
                            }}
                          >
                            告警
                          </Text>
                        </div>
                      </div>
                    </div>
                  </Spin>
                )}
              </div>
            </Spin>
          )}
        </div>
      </Card>

      {/* 修改资料模态框 */}
      <Modal
        title="修改个人资料"
        open={editProfileModalVisible}
        onCancel={() => {
          setEditProfileModalVisible(false);
          setCurrentStep(0);
        }}
        footer={[
          currentStep === 1 && (
            <Button key="back" onClick={() => setCurrentStep(0)}>
              上一步
            </Button>
          ),
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              if (currentStep === 0) {
                editProfileForm.validateFields().then(() => {
                  setCurrentStep(1);
                });
              } else {
                editProfileForm.validateFields().then((values) => {
                  console.log("个人资料表单值:", values);
                  // 提交表单，这里简化处理，只输出到控制台
                  setEditProfileModalVisible(false);
                  setCurrentStep(0);
                });
              }
            }}
          >
            {currentStep === 0 ? "下一步" : "确定"}
          </Button>,
        ]}
      >
        <Steps current={currentStep} style={{ marginBottom: 16 }}>
          <Step title="填写信息" />
          <Step title="安全验证" />
        </Steps>

        <Form form={editProfileForm} layout="vertical" requiredMark={false}>
          {currentStep === 0 ? (
            <>
              <Form.Item
                name="name"
                label="用户名"
                rules={[{ required: true, message: "请输入用户名" }]}
              >
                <Input placeholder="请输入用户名" />
              </Form.Item>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: "请输入邮箱地址" },
                  { type: "email", message: "请输入有效的邮箱地址" },
                ]}
              >
                <Input placeholder="请输入邮箱地址" />
              </Form.Item>
              <Form.Item
                name="telephone"
                label="手机号"
                rules={[
                  { required: true, message: "请输入手机号" },
                  { pattern: /^1\d{10}$/, message: "请输入有效的手机号" },
                ]}
              >
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </>
          ) : (
            <div style={{ textAlign: "center" }}>
              <div style={{ margin: "12px 0", textAlign: "center" }}>
                <Text>
                  验证码已发送至您的手机号{" "}
                  <Text strong>{editProfileForm.getFieldValue("telephone")}</Text>
                </Text>
              </div>
              <Form.Item
                name="verificationCode"
                label="验证码"
                rules={[{ required: true, message: "请输入验证码" }]}
              >
                <Input
                  placeholder="请输入6位验证码"
                  maxLength={6}
                  style={{ width: "50%", textAlign: "center" }}
                />
              </Form.Item>
              <Button type="link" style={{ padding: 0 }}>
                重新发送验证码
              </Button>
            </div>
          )}
        </Form>
      </Modal>

      {/* 订阅套餐模态框 */}
      <Modal
        title="订阅套餐"
        open={subscriptionModalVisible}
        onCancel={() => setSubscriptionModalVisible(false)}
        footer={null}
        width={800}
      >
        <div
          style={{
            background: "#f9f9f9",
            padding: 12,
            borderRadius: 8,
            marginBottom: 16,
          }}
        >
          <Flex justify="space-between" align="center">
            <Text strong>当前套餐: </Text>
            <Tag color="green" style={{ marginLeft: 8, fontSize: 13 }}>
              {
                subscriptionPlans.find(
                  (p) => p.id === currentSubscription.planId
                )?.name
              }
            </Tag>
            <Text type="secondary">
              到期时间: {currentSubscription.expires}
            </Text>
          </Flex>
        </div>

        <Row gutter={24}>
          {subscriptionPlans.map((plan) => (
            <Col span={8} key={plan.id}>
              <div
                style={{
                  height: "100%",
                  borderRadius: 8,
                  border: `1px solid ${
                    plan.id === currentSubscription.planId
                      ? "#52c41a"
                      : "#d9d9d9"
                  }`,
                  background: "#fff",
                  position: "relative",
                  overflow: "hidden",
                }}
              >
                {plan.id === currentSubscription.planId && (
                  <Tag
                    color="green"
                    style={{
                      position: "absolute",
                      top: -10,
                      right: -10,
                      borderRadius: 2,
                      boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                    }}
                  >
                    当前套餐
                  </Tag>
                )}
                <div style={{ padding: 16 }}>
                  <Title
                    level={4}
                    style={{ textAlign: "center", margin: "12px 0 8px" }}
                  >
                    {plan.name}
                  </Title>
                  <Flex vertical align="center" style={{ marginBottom: 12 }}>
                    {plan.price > 0 ? (
                      <>
                        <Title level={2} style={{ marginBottom: 0 }}>
                          ¥{plan.price}
                        </Title>
                        <Text type="secondary">/月</Text>
                      </>
                    ) : (
                      <Title
                        level={2}
                        style={{ color: "#52c41a", marginBottom: 0 }}
                      >
                        免费
                      </Title>
                    )}
                    <Text type="secondary" style={{ marginTop: 4 }}>
                      {plan.description}
                    </Text>
                  </Flex>

                  <Divider style={{ margin: "8px 0" }} />

                  <div style={{ minHeight: 170 }}>
                    {plan.features.map((feature, index) => (
                      <Space
                        key={index}
                        align="start"
                        style={{ marginBottom: 6 }}
                      >
                        <CheckOutlined
                          style={{
                            color: "#52c41a",
                            marginRight: 8,
                            marginTop: 4,
                          }}
                        />
                        <Text>{feature}</Text>
                      </Space>
                    ))}
                  </div>

                  {plan.id !== currentSubscription.planId ? (
                    <Button
                      type="primary"
                      block
                      style={{
                        marginTop: 12,
                        boxShadow: "0 2px 8px rgba(24, 144, 255, 0.3)",
                      }}
                      onClick={() => {
                        console.log("选择套餐:", plan);
                        setSubscriptionModalVisible(false);
                      }}
                    >
                      立即订阅
                    </Button>
                  ) : (
                    <Button
                      block
                      style={{
                        marginTop: 12,
                        background: "#f6ffed",
                        borderColor: "#b7eb8f",
                        color: "#389e0d",
                      }}
                      disabled
                    >
                      当前套餐
                    </Button>
                  )}
                </div>
              </div>
            </Col>
          ))}
        </Row>

        <Flex justify="center" style={{ marginTop: 20 }}>
          <Text type="secondary">订阅服务自动续费，可随时取消</Text>
        </Flex>
      </Modal>

      {/* 退出登录确认模态框 */}
      <Modal
        title="确认退出登录"
        open={logoutModalVisible}
        onCancel={() => setLogoutModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setLogoutModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="confirm"
            type="primary"
            danger
            loading={logoutLoading}
            onClick={handleLogout}
          >
            确认退出
          </Button>,
        ]}
        width={400}
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <LogoutOutlined style={{ fontSize: 48, color: '#ff4d4f', marginBottom: 16 }} />
          <div style={{ marginBottom: 8 }}>
            <Text strong style={{ fontSize: 16 }}>您确定要退出登录吗？</Text>
          </div>
          <Text type="secondary">
            退出后您需要重新登录才能继续使用系统
          </Text>
        </div>
      </Modal>
    </>
  );
};

export default UserProfileCard;